(()=>{var t={n:n=>{var i=n&&n.__esModule?()=>n.default:()=>n;return t.d(i,{a:i}),i},d:(n,i)=>{for(var a in i)t.o(i,a)&&!t.o(n,a)&&Object.defineProperty(n,a,{enumerable:!0,get:i[a]})},o:(t,n)=>Object.prototype.hasOwnProperty.call(t,n)};(()=>{"use strict";function n(t,i){return n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},n(t,i)}function i(t,i){t.prototype=Object.create(i.prototype),t.prototype.constructor=t,n(t,i)}flarum.core.compat.extend;const a=flarum.core.compat["components/ExtensionPage"];var o=t.n(a);const e=flarum.core.compat["components/LoadingIndicator"];var r=t.n(e);const s=flarum.core.compat["components/Button"];var d=t.n(s);const l=flarum.core.compat.Component;var u=t.n(l);const c=flarum.core.compat["components/Modal"];var p=function(t){function n(){return t.apply(this,arguments)||this}i(n,t);var a=n.prototype;return a.oninit=function(n){t.prototype.oninit.call(this,n),this.biddingRankListItem=this.attrs.biddingRankListItem,this.loading=!1},a.className=function(){return"Modal--small"},a.title=function(){return app.translator.trans("wusong8899-bidding-rank.admin.delete-confirmation")},a.content=function(){var t=this;return m("div",{className:"Modal-body"},m("div",{className:"Form-group",style:"text-align: center;"},d().component({className:"Button Button--primary",type:"submit",loading:this.loading},app.translator.trans("wusong8899-bidding-rank.admin.confirm"))," ",d().component({className:"Button",loading:this.loading,onclick:function(){t.hide()}},app.translator.trans("wusong8899-bidding-rank.admin.cancel"))))},a.onsubmit=function(t){var n=this;t.preventDefault(),this.loading=!0,this.biddingRankListItem.save({isDelete:1}).then(function(){return location.reload()},function(t){n.loading=!1})},n}(t.n(c)());p.isDismissibleViaBackdropClick=!1,p.isDismissibleViaCloseButton=!0;const g=flarum.core.compat["helpers/username"];var b=t.n(g),f=function(t){function n(){return t.apply(this,arguments)||this}i(n,t);var a=n.prototype;return a.view=function(){var t=this,n=this.attrs,i=n.biddingRankListItem,a=(n.rankID,app.forum.attribute("antoinefr-money.moneyname")||"[money]"),o=i.attribute("bid"),e=i.fromUser(),r=(i.url(),i.content()),s=i.title();return a.replace("[money]",o),m("div",{className:"biddingRankSettingContainer"},m("div",{style:"float:right"},d().component({style:"font-weight:bold;",className:"Button Button--danger",onclick:function(n){t.deleteItem(i)}},app.translator.trans("wusong8899-bidding-rank.admin.delete"))),m("div",null,m("b",null,app.translator.trans("wusong8899-bidding-rank.admin.bidding-rank-list-username"),": "),b()(e)," | ",m("b",null,app.translator.trans("wusong8899-bidding-rank.admin.bidding-rank-list-title"),": "),s," | ",m("b",null,app.translator.trans("wusong8899-bidding-rank.admin.bidding-rank-list-bid"),": "),o),m("div",null,m("b",null,app.translator.trans("wusong8899-bidding-rank.admin.bidding-rank-list-content"),": "),r))},a.deleteItem=function(t){app.modal.show(p,{biddingRankListItem:t})},n}(u());const h=flarum.core.compat["admin/app"];var k=t.n(h),y=function(t){function n(){return t.apply(this,arguments)||this}i(n,t);var a=n.prototype;return a.oninit=function(n){t.prototype.oninit.call(this,n),this.loading=!0,this.moreResults=!1,this.biddingRankList=[],this.loadResults()},a.content=function(){var t,n=this;return this.loading&&(t=r().component({size:"large"})),m("div",{className:"ExtensionPage-settings FlarumBadgesPage"},m("div",{className:"container"},m("ul",{style:"padding:0px;list-style-type: none;"},this.biddingRankList.map(function(t){return m("li",{itemID:t.id(),style:"margin-top:5px;background: var(--body-bg);"},f.component({biddingRankListItem:t}))})),!this.loading&&0===this.biddingRankList.length&&m("div",null,m("div",{style:"font-size:1.4em;color: var(--muted-more-color);text-align: center;line-height: 100px;"},k().translator.trans("wusong8899-bidding-rank.forum.list-empty"))),!t&&this.hasMoreResults()&&m("div",{style:"text-align:center;padding:20px"},m(d(),{className:"Button Button--primary",disabled:this.loading,loading:this.loading,onclick:function(){return n.loadMore()}},k().translator.trans("wusong8899-bidding-rank.admin.list-load-more"))),t&&m("div",{className:"BiddingRankContent-loadMore"},t)))},a.hasMoreResults=function(){return this.moreResults},a.loadMore=function(){this.loading=!0,this.loadResults(this.biddingRankList.length)},a.parseResults=function(t){var n=this;return this.moreResults=!!t.payload.links&&!!t.payload.links.next,[].push.apply(this.biddingRankList,t),k().store.find("biddingRankHistory").catch(function(){}).then(function(i){return n.hasBid=i.length>0,n.hasBid&&(n.biddingRankHistoryData=i[0]),n.loading=!1,m.redraw(),t})},a.loadResults=function(t){return void 0===t&&(t=0),k().store.find("biddingRankList",{page:{offset:t}}).catch(function(){}).then(this.parseResults.bind(this))},n}(o());const v=flarum.core.compat.Model;var R=t.n(v),w=function(t){function n(){return t.apply(this,arguments)||this}return i(n,t),n}(R());Object.assign(w.prototype,{id:R().attribute("id"),title:R().attribute("title"),content:R().attribute("content"),user_id:R().attribute("user_id"),url:R().attribute("url"),bid:R().attribute("bid"),fromUser:R().hasOne("fromUser")}),app.initializers.add("wusong8899-client1-bidding-rank-content",function(){app.store.models.biddingRankList=w,app.extensionData.for("wusong8899-bidding-rank-content").registerPage(y)})})(),module.exports={}})();
//# sourceMappingURL=admin.js.map