{"version": 3, "file": "admin.js", "mappings": "MACA,IAAIA,EAAsB,CCA1BA,EAAyBC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,WAC7B,IAAOF,EAAiB,QACxB,IAAM,EAEP,OADAD,EAAoBI,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRF,EAAwB,CAACM,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXP,EAAoBS,EAAEF,EAAYC,KAASR,EAAoBS,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3ER,EAAwB,CAACc,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,I,mBCAlF,SAASI,EAAgBC,EAAGC,GAC1B,OAAOF,EAAkBT,OAAOY,eAAiBZ,OAAOY,eAAeC,OAAS,SAAUH,EAAGC,GAC3F,OAAOD,EAAEI,UAAYH,EAAGD,CAC1B,EAAGD,EAAgBC,EAAGC,EACxB,CCHA,SAASI,EAAeL,EAAGX,GACzBW,EAAEJ,UAAYN,OAAOgB,OAAOjB,EAAEO,WAAYI,EAAEJ,UAAUW,YAAcP,EAAGE,EAAeF,EAAGX,EAC3F,CCHqCmB,OAAOC,KAAKC,OAAe,OCAhE,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,4B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,+B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,qB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAkB,U,aCAnE,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,oB,ICGnCC,EAAsB,SAAAC,GAAA,SAAAD,IAAA,OAAAC,EAAAC,MAAA,KAAAC,YAAA,KAAAT,EAAAM,EAAAC,GAAA,IAAAG,EAAAJ,EAAAf,UA2DxC,OA3DwCmB,EAIzCC,OAAA,SAAOC,GACLL,EAAAhB,UAAMoB,OAAMlB,KAAC,KAAAmB,GACbC,KAAKC,oBAAsBD,KAAKE,MAAMD,oBACtCD,KAAKG,SAAU,CACjB,EAACN,EAEDO,UAAA,WACE,MAAO,cACT,EAACP,EAEDQ,MAAA,WACE,OAAOC,IAAIC,WAAWC,MAAM,oDAC9B,EAACX,EAEDY,QAAA,WAAU,IAAAC,EAAA,KAER,OACEC,EAAA,OAAKP,UAAU,cACbO,EAAA,OAAKP,UAAU,aAAaQ,MAAM,uBAC/BC,IAAAA,UACC,CACET,UAAW,yBACXU,KAAM,SACNX,QAASH,KAAKG,SAEhBG,IAAIC,WAAWC,MAAM,0CACrB,IACDK,IAAAA,UACC,CACET,UAAW,SACXD,QAASH,KAAKG,QACdY,QAAS,WACPL,EAAKM,MACP,GAEFV,IAAIC,WAAWC,MAAM,0CAK/B,EAACX,EAEDoB,SAAA,SAASlC,GAAG,IAAAmC,EAAA,KACVnC,EAAEoC,iBAEFnB,KAAKG,SAAU,EACfH,KAAKC,oBAAoBmB,KAAK,CAC5BC,SAAS,IAEVC,KACC,kBAAMC,SAASC,QAAQ,EACvB,SAACC,GACCP,EAAKf,SAAU,CACjB,EAEJ,EAACV,CAAA,CA3DwC,C,MAASiC,IAA/BjC,EACZkC,+BAAgC,EADpBlC,EAEZmC,6BAA8B,ECLvC,MAAM,EAA+BtC,OAAOC,KAAKC,OAAO,oB,aCKnCqC,EAAmB,SAAAC,GAAA,SAAAD,IAAA,OAAAC,EAAAnC,MAAA,KAAAC,YAAA,KAAAT,EAAA0C,EAAAC,GAAA,IAAAjC,EAAAgC,EAAAnD,UA2CrC,OA3CqCmB,EAEtCkC,KAAA,WAAO,IAAArB,EAAA,KACLsB,EAAqChC,KAAKE,MAAnCD,EAAmB+B,EAAnB/B,oBACDgC,GAD2BD,EAANE,OACT5B,IAAI6B,MAAMC,UAAU,8BAAgC,WAChEC,EAAWpC,EAAoBmC,UAAU,OACzCE,EAAUrC,EAAoBsC,WAE9BC,GADSvC,EAAoBwC,MAChBxC,EAAoBQ,WACjCiC,EAAWzC,EAAoBI,QAGrC,OAFgB4B,EAAUU,QAAQ,UAAWN,GAG3C1B,EAAA,OAAKP,UAAU,+BACbO,EAAA,OAAKC,MAAM,eACRC,IAAAA,UAAiB,CAChBD,MAAO,oBACPR,UAAW,wBACXW,QAAS,SAAChC,GACR2B,EAAKkC,WAAW3C,EAClB,GAEFK,IAAIC,WAAWC,MAAM,0CAGvBG,EAAA,WACEA,EAAA,SAAIL,IAAIC,WAAWC,MAAM,4DAA4D,MACpFqC,IAASP,GAAS,MACnB3B,EAAA,SAAIL,IAAIC,WAAWC,MAAM,yDAAyD,MACjFkC,EAAS,MACV/B,EAAA,SAAIL,IAAIC,WAAWC,MAAM,uDAAuD,MAC/E6B,GAEH1B,EAAA,WACEA,EAAA,SAAIL,IAAIC,WAAWC,MAAM,2DAA2D,MACnFgC,GAIT,EAAC3C,EAED+C,WAAA,SAAW3C,GACTK,IAAIwC,MAAMC,KAAKtD,EAAwB,CAACQ,oBAAAA,GAC1C,EAAC4B,CAAA,CA3CqC,CAASmB,KCLjD,MAAM,EAA+B1D,OAAOC,KAAKC,OAAO,a,aCOnCyD,EAAmB,SAAAC,GAAA,SAAAD,IAAA,OAAAC,EAAAvD,MAAA,KAAAC,YAAA,KAAAT,EAAA8D,EAAAC,GAAA,IAAArD,EAAAoD,EAAAvE,UA4FrC,OA5FqCmB,EACtCC,OAAA,SAAOI,GACLgD,EAAAxE,UAAMoB,OAAMlB,KAAC,KAAAsB,GACbF,KAAKG,SAAU,EACfH,KAAKmD,aAAc,EACnBnD,KAAKoD,gBAAkB,GACvBpD,KAAKqD,aACP,EAACxD,EAEDY,QAAA,WAAU,IACJN,EADIO,EAAA,KAOR,OAJGV,KAAKG,UACNA,EAAUmD,IAAAA,UAA2B,CAAEC,KAAM,WAI7C5C,EAAA,OAAKP,UAAU,2CACbO,EAAA,OAAKP,UAAU,aAEbO,EAAA,MAAIC,MAAM,sCACPZ,KAAKoD,gBAAgBI,IAAI,SAACvD,GACzB,OACEU,EAAA,MAAI8C,OAAQxD,EAAoByD,KAAM9C,MAAM,8CACzCiB,EAAoB8B,UAAU,CAAE1D,oBAAAA,IAGvC,KAGAD,KAAKG,SAAyC,IAA9BH,KAAKoD,gBAAgBQ,QACrCjD,EAAA,WACEA,EAAA,OAAKC,MAAM,yFAAyFN,IAAAA,WAAeE,MAAM,+CAI3HL,GAAWH,KAAK6D,kBAChBlD,EAAA,OAAKC,MAAM,kCACTD,EAACE,IAAM,CAACT,UAAW,yBAA0B0D,SAAU9D,KAAKG,QAASA,QAASH,KAAKG,QAASY,QAAS,WAAF,OAAQL,EAAKqD,UAAU,GACvHzD,IAAAA,WAAeE,MAAM,kDAK3BL,GAAWQ,EAAA,OAAKP,UAAU,+BAA+BD,IAKlE,EAACN,EAEDgE,eAAA,WACE,OAAO7D,KAAKmD,WACd,EAACtD,EAEDkE,SAAA,WACE/D,KAAKG,SAAU,EACfH,KAAKqD,YAAYrD,KAAKoD,gBAAgBQ,OACxC,EAAC/D,EAEDmE,aAAA,SAAaC,GAAS,IAAA/C,EAAA,KAIpB,OAHAlB,KAAKmD,cAAgBc,EAAQC,QAAQC,SAAWF,EAAQC,QAAQC,MAAMC,KACtE,GAAGC,KAAK1E,MAAMK,KAAKoD,gBAAiBa,GAE7B3D,IAAAA,MACJgE,KAAK,sBAAqB,MACpB,WAAO,GACbhD,KAAK,SAACiD,GAUL,OATArD,EAAKsD,OAASD,EAAOX,OAAO,EAEzB1C,EAAKsD,SACNtD,EAAKuD,uBAAyBF,EAAO,IAGvCrD,EAAKf,SAAU,EACfQ,EAAE+D,SAEKT,CACT,EAGJ,EAACpE,EAEDwD,YAAA,SAAYsB,GACV,YADgB,IAANA,IAAAA,EAAS,GACZrE,IAAAA,MACJgE,KAAK,kBAAmB,CACvBM,KAAM,CACJD,OAAAA,KAEF,MACK,WAAO,GACbrD,KAAKtB,KAAKgE,aAAa/E,KAAKe,MACjC,EAACiD,CAAA,CA5FqC,CAAS4B,KCPjD,MAAM,EAA+BvF,OAAOC,KAAKC,OAAc,M,aCE1CsF,EAAW,SAAAC,GAAA,SAAAD,IAAA,OAAAC,EAAApF,MAAA,KAAAC,YAAA,YAAAT,EAAA2F,EAAAC,GAAAD,CAAA,EAASE,KACzC5G,OAAO6G,OAAOH,EAAYpG,UAAW,CACnCgF,GAAIsB,IAAAA,UAAgB,MACpB3E,MAAO2E,IAAAA,UAAgB,SACvBvE,QAASuE,IAAAA,UAAgB,WACzBE,QAASF,IAAAA,UAAgB,WACzBvC,IAAKuC,IAAAA,UAAgB,OACrBG,IAAKH,IAAAA,UAAgB,OACrBzC,SAAUyC,IAAAA,OAAa,cCNzB1E,IAAI8E,aAAaC,IAAI,0CAA2C,WAC9D/E,IAAIgF,MAAMC,OAAOnC,gBAAkB0B,EACnCxE,IAAIkF,cAAa,IAAK,mCAAmCC,aAAaC,EACxE,E", "sources": ["webpack://@wusong8899/bidding-rank-content/webpack/bootstrap", "webpack://@wusong8899/bidding-rank-content/webpack/runtime/compat get default export", "webpack://@wusong8899/bidding-rank-content/webpack/runtime/define property getters", "webpack://@wusong8899/bidding-rank-content/webpack/runtime/hasOwnProperty shorthand", "webpack://@wusong8899/bidding-rank-content/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "webpack://@wusong8899/bidding-rank-content/./node_modules/.pnpm/@babel+runtime@7.28.2/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['extend']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['components/ExtensionPage']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['components/LoadingIndicator']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['components/Button']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['Component']\"", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['components/Modal']\"", "webpack://@wusong8899/bidding-rank-content/./src/admin/components/BiddingRankDeleteModal.js", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['helpers/username']\"", "webpack://@wusong8899/bidding-rank-content/./src/admin/components/BiddingRankListItem.js", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['admin/app']\"", "webpack://@wusong8899/bidding-rank-content/./src/admin/components/SettingsPage.js", "webpack://@wusong8899/bidding-rank-content/external root \"flarum.core.compat['Model']\"", "webpack://@wusong8899/bidding-rank-content/./src/forum/model/BiddingRank.js", "webpack://@wusong8899/bidding-rank-content/./src/admin/index.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };", "import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inheritsLoose(t, o) {\n  t.prototype = Object.create(o.prototype), t.prototype.constructor = t, setPrototypeOf(t, o);\n}\nexport { _inheritsLoose as default };", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['extend'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/ExtensionPage'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LoadingIndicator'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Button'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['Component'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Modal'];", "import Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\n\nexport default class BiddingRankDeleteModal extends Modal {\n  static isDismissibleViaBackdropClick = false;\n  static isDismissibleViaCloseButton = true;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.biddingRankListItem = this.attrs.biddingRankListItem;\n    this.loading = false;\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-bidding-rank.admin.delete-confirmation');\n  }\n\n  content() {\n    //\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form-group\" style=\"text-align: center;\">\n          {Button.component(\n            {\n              className: 'Button Button--primary',\n              type: 'submit',\n              loading: this.loading,\n            },\n            app.translator.trans('wusong8899-bidding-rank.admin.confirm')\n          )}&nbsp;\n          {Button.component(\n            {\n              className: 'Button',\n              loading: this.loading,\n              onclick: () => {\n                this.hide();\n              }\n            },\n            app.translator.trans('wusong8899-bidding-rank.admin.cancel')\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  onsubmit(e) {\n    e.preventDefault();\n\n    this.loading = true;\n    this.biddingRankListItem.save({\n      isDelete:1,\n    })\n    .then(\n      () => location.reload(),\n      (response) => {\n        this.loading = false;\n      }\n    );\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['helpers/username'];", "import Component from \"flarum/Component\";\nimport Button from 'flarum/components/Button';\nimport BiddingRankDeleteModal from './BiddingRankDeleteModal';\nimport username from \"flarum/helpers/username\";\n\nexport default class BiddingRankListItem extends Component {\n\n  view() {\n    const {biddingRankListItem,rankID} = this.attrs;\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    const bidValue = biddingRankListItem.attribute(\"bid\");\n    const bidUser = biddingRankListItem.fromUser();\n    const bidURL = biddingRankListItem.url();\n    const bidContent = biddingRankListItem.content();\n    const bidTitle = biddingRankListItem.title();\n    const bidText = moneyName.replace('[money]', bidValue);\n\n    return (\n      <div className=\"biddingRankSettingContainer\">\n        <div style=\"float:right\">\n          {Button.component({\n            style: \"font-weight:bold;\",\n            className: 'Button Button--danger',\n            onclick: (e) => {\n              this.deleteItem(biddingRankListItem)\n            }\n          },\n          app.translator.trans('wusong8899-bidding-rank.admin.delete')\n          )}\n        </div>\n        <div>\n          <b>{app.translator.trans('wusong8899-bidding-rank.admin.bidding-rank-list-username')}: </b>\n          {username(bidUser)}&nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-bidding-rank.admin.bidding-rank-list-title')}: </b>\n          {bidTitle}&nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-bidding-rank.admin.bidding-rank-list-bid')}: </b>\n          {bidValue}\n        </div>\n        <div>\n          <b>{app.translator.trans('wusong8899-bidding-rank.admin.bidding-rank-list-content')}: </b>\n          {bidContent}\n        </div>\n      </div>\n    );\n  }\n\n  deleteItem(biddingRankListItem){\n    app.modal.show(BiddingRankDeleteModal, {biddingRankListItem});\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['admin/app'];", "import ExtensionPage from 'flarum/components/ExtensionPage';\nimport LoadingIndicator from 'flarum/components/LoadingIndicator';\nimport Button from 'flarum/components/Button';\nimport BiddingRankListItem from './BiddingRankListItem';\nimport app from 'flarum/admin/app';\n\n\nexport default class BiddingRankSettings extends ExtensionPage {\n  oninit(attrs) {\n    super.oninit(attrs);\n    this.loading = true;\n    this.moreResults = false;\n    this.biddingRankList = [];\n    this.loadResults();\n  }\n\n  content() {\n    let loading;\n\n    if(this.loading){\n      loading = LoadingIndicator.component({ size: \"large\" });\n    }\n\n    return (\n      <div className=\"ExtensionPage-settings FlarumBadgesPage\">\n        <div className=\"container\">\n\n          <ul style=\"padding:0px;list-style-type: none;\">\n            {this.biddingRankList.map((biddingRankListItem) => {\n              return (\n                <li itemID={biddingRankListItem.id()} style=\"margin-top:5px;background: var(--body-bg);\">\n                  {BiddingRankListItem.component({ biddingRankListItem })}\n                </li>\n              );\n            })}\n          </ul>\n\n          {!this.loading && this.biddingRankList.length===0 && (\n            <div>\n              <div style=\"font-size:1.4em;color: var(--muted-more-color);text-align: center;line-height: 100px;\">{app.translator.trans(\"wusong8899-bidding-rank.forum.list-empty\")}</div>\n            </div>\n          )}\n\n          {!loading && this.hasMoreResults() && (\n            <div style=\"text-align:center;padding:20px\">\n              <Button className={'Button Button--primary'} disabled={this.loading} loading={this.loading} onclick={() => this.loadMore()}>\n                {app.translator.trans('wusong8899-bidding-rank.admin.list-load-more')}\n              </Button>\n            </div>\n          )}\n\n          {loading && <div className=\"BiddingRankContent-loadMore\">{loading}</div>}\n\n        </div>\n      </div>\n    );\n  }\n  \n  hasMoreResults() {\n    return this.moreResults;\n  }\n\n  loadMore() {\n    this.loading = true;\n    this.loadResults(this.biddingRankList.length);\n  }\n\n  parseResults(results) {\n    this.moreResults = !!results.payload.links && !!results.payload.links.next;\n    [].push.apply(this.biddingRankList, results);\n\n    return app.store\n      .find(\"biddingRankHistory\")\n      .catch(() => {})\n      .then((result) => {\n        this.hasBid = result.length>0;\n        \n        if(this.hasBid){\n          this.biddingRankHistoryData = result[0];\n        }\n\n        this.loading = false;\n        m.redraw();\n\n        return results;\n      });\n    \n    return results;\n  }\n\n  loadResults(offset = 0) {\n    return app.store\n      .find(\"biddingRankList\", {\n        page: {\n          offset\n        },\n      })\n      .catch(() => {})\n      .then(this.parseResults.bind(this));\n  }\n\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['Model'];", "import Model from \"flarum/Model\";\n\nexport default class BiddingRank extends Model {}\nObject.assign(BiddingRank.prototype, {\n  id: Model.attribute(\"id\"),\n  title: Model.attribute(\"title\"),\n  content: Model.attribute(\"content\"),\n  user_id: Model.attribute(\"user_id\"),\n  url: Model.attribute(\"url\"),\n  bid: Model.attribute(\"bid\"),\n  fromUser: Model.hasOne(\"fromUser\"),\n});\n", "import {extend, override} from 'flarum/extend';\nimport SettingsPage from './components/SettingsPage';\nimport BiddingRank from \"../forum/model/BiddingRank\";\n\napp.initializers.add('wusong8899-client1-bidding-rank-content', () => {\n  app.store.models.biddingRankList = BiddingRank;\n  app.extensionData.for('wusong8899-bidding-rank-content').registerPage(SettingsPage);\n});\n"], "names": ["__webpack_require__", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "_setPrototypeOf", "t", "e", "setPrototypeOf", "bind", "__proto__", "_inherits<PERSON><PERSON>e", "create", "constructor", "flarum", "core", "compat", "BiddingRankDeleteModal", "_Modal", "apply", "arguments", "_proto", "oninit", "vnode", "this", "biddingRankListItem", "attrs", "loading", "className", "title", "app", "translator", "trans", "content", "_this", "m", "style", "<PERSON><PERSON>", "type", "onclick", "hide", "onsubmit", "_this2", "preventDefault", "save", "isDelete", "then", "location", "reload", "response", "Modal", "isDismissibleViaBackdropClick", "isDismissibleViaCloseButton", "BiddingRankListItem", "_Component", "view", "_this$attrs", "moneyName", "rankID", "forum", "attribute", "bidValue", "bidUser", "fromUser", "bidContent", "url", "bidTitle", "replace", "deleteItem", "username", "modal", "show", "Component", "BiddingRankSettings", "_ExtensionPage", "moreResults", "biddingRankList", "loadResults", "LoadingIndicator", "size", "map", "itemID", "id", "component", "length", "hasMoreResults", "disabled", "loadMore", "parseResults", "results", "payload", "links", "next", "push", "find", "result", "hasBid", "biddingRankHistoryData", "redraw", "offset", "page", "ExtensionPage", "BiddingRank", "_Model", "Model", "assign", "user_id", "bid", "initializers", "add", "store", "models", "extensionData", "registerPage", "SettingsPage"], "sourceRoot": ""}