
.BiddingRangContainer{
  width: 100%; 
  position: relative;

  @media @desktop-up {
    padding-top: 30px;
  }
  @media @tablet {
    padding-top: 30px;
  }
  @media @phone {
    padding-top: 10px;
  }
}

.BiddingRankListTitle{
  font-size: 24px;
  font-weight: bold;
}

.BiddingRankList{
  margin: 0;
  padding: 0px 0px 20px 0px;
  list-style-type: none;
  position: relative;

  height: 500px;
  overflow-y: auto;
}

.BiddingRankListItemContainer{
  display: flex;
  border-radius: 4px;
  padding: 10px;
}

.BiddingRankListItems{
  margin-top:4px;
}

.BiddingRankListHeaderContent{
  width:80%;
  position: relative;
  font-size:12px;
}

.BiddingRankListHeaderMoney{
  width:20%;
  color:#64ca47;
}

.BiddingRankListHeader{
  display: flex;
  font-size: 12px;
  padding:10px 0px;
}


.BiddingRankDataLabel{
  text-align: left;
  padding: 10px 0px;
  font-size: 14px;
  font-weight: bold;
}

.u-sprites-money {
    display: inline-block;
    vertical-align: middle;
    background-image: url(https://mutluresim.com/images/2023/03/08/uYRpr.png);
}
.u-sprites-currency-cny {
    width: 60px;
    height: 60px;
    background-position: -128px -128px;
}

.u-sprites-currency {
    -webkit-transform: scale(.4);
    transform: scale(.4);
    -webkit-transform-origin: 0 bottom;
    transform-origin: 0 bottom;
    margin-right: -32px;
    margin-top: -38px;
}